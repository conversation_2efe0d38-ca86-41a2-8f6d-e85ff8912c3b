user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log notice;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    log_format json_combined escape=json
      '{'
        '"time_local":"$time_local",'
        '"remote_addr":"$remote_addr",'
        '"remote_user":"$remote_user",'
        '"request":"$request",'
        '"status": "$status",'
        '"body_bytes_sent":"$body_bytes_sent",'
        '"request_time":"$request_time",'
        '"http_referrer":"$http_referer",'
        '"http_user_agent":"$http_user_agent",'
        '"http_x_real_ip":"$http_x_real_ip",'
        '"http_x_forwarded_for":"$http_x_forwarded_for",'
        '"http_x_forwarded_proto":"$http_x_forwarded_proto",'
        '"upstream_addr":"$upstream_addr",'
        '"upstream_status":"$upstream_status",'
        '"upstream_response_time":"$upstream_response_time"'
      '}';

    access_log /var/log/nginx/access.log json_combined;

    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 10240;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # 限流
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;

    # 上游服务器定义
    upstream api_gateway {
        least_conn;
        server api-gateway-prod:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream chat_service {
        least_conn;
        server chat-service-prod:8001 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }

    upstream agent_service {
        least_conn;
        server agent-service-prod:8002 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }

    upstream rag_service {
        least_conn;
        server rag-service-prod:8003 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }

    upstream user_service {
        least_conn;
        server user-service-prod:8004 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }

    upstream n8n_service {
        server n8n-prod:5678 max_fails=3 fail_timeout=30s;
    }

    upstream grafana_service {
        server grafana-prod:3000 max_fails=3 fail_timeout=30s;
    }

    # 包含虚拟主机配置
    include /etc/nginx/conf.d/*.conf;
} 