[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-travel-planner"
version = "1.0.0"
description = "AI-powered travel planning agent with multi-service architecture"
authors = [
    {name = "AI Travel Planner Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10"
keywords = ["ai", "travel", "planning", "assistant", "llm", "agents", "rag", "mcp"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    # Web框架
    "fastapi==0.115.6",
    "uvicorn[standard]==0.32.1",
    "websockets==14.1",
    "starlette==0.41.3",
    
    # AI/ML框架
    "langchain==0.3.12",
    "langchain-community==0.3.12",
    "langchain-openai==0.2.14",
    "openai==1.61.0",
    "transformers==4.51.1",
    "sentence-transformers==3.3.1",
    "vllm==0.8.5",
    "torch==2.6.0",
    
    # 数据验证和ORM
    "pydantic==2.10.4",
    "pydantic-settings==2.7.0",
    "sqlalchemy==2.0.36",
    "alembic==1.14.0",
    "aiomysql==0.2.0",
    
    # 缓存和消息队列
    "redis==5.2.1",
    "celery==5.4.0",
    
    # 向量数据库
    "qdrant-client==1.14.1",
    
    # HTTP客户端
    "httpx==0.28.1",
    "aiohttp==3.11.11",
    "requests==2.32.3",
    
    # 数据处理
    "numpy>=1.24.0",
    "pandas>=2.0.0",
    "beautifulsoup4==4.12.3",
    "python-multipart==0.0.20",
    
    # 配置管理
    "python-dotenv==1.0.1",
    "pyyaml==6.0.2",
    
    # 认证和安全
    "python-jose[cryptography]==3.3.0",
    "passlib[bcrypt]==1.7.4",
    "cryptography==44.0.0",
    
    # 监控和日志
    "prometheus-client==0.21.1",
    "structlog==24.4.0",
    "loguru==0.7.3",
    
    # 模板引擎
    "jinja2==3.1.4",
]

[project.optional-dependencies]
dev = [
    # 测试工具
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "factory-boy==3.3.1",
    "freezegun==1.5.3",
    "responses==0.25.3",
    
    # 代码质量
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "pre-commit>=3.0.0",
    "mypy==1.14.0",
    
    # 开发工具
    "rich==14.0.0",
    "typer==0.15.1",
    "jupyter>=1.0.0",
    "jupyterlab>=4.0.0",
]

vllm = [
    "vllm==0.8.5",
    "torch==2.5.1",
    "transformers==4.47.1",
    "accelerate==1.2.1",
]

[project.urls]
Homepage = "https://github.com/ai-travel-team/ai-travel-planner"
Repository = "https://github.com/ai-travel-team/ai-travel-planner.git"
Issues = "https://github.com/ai-travel-team/ai-travel-planner/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["shared*", "services*"]

# Black配置
[tool.black]
line-length = 88
target-version = ['py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  migrations
  | \.git
  | \.venv
  | build
  | dist
  | node_modules
  | __pycache__
)/
'''

# isort配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["shared", "services"]
known_third_party = ["fastapi", "pydantic", "sqlalchemy", "langchain", "openai"]
skip_glob = ["migrations/*", "*/migrations/*"]

# Pytest配置
[tool.pytest.ini_options]
testpaths = ["tests", "services/*/tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--cov=shared",
    "--cov=services",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--strict-markers",
    "-v",
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "e2e: End-to-end tests",
    "slow: Slow running tests",
    "requires_gpu: Tests that require GPU",
    "requires_db: Tests that require database",
    "requires_redis: Tests that require Redis",
]
asyncio_mode = "auto"

# MyPy配置
[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "langchain.*",
    "openai.*",
    "redis.*",
    "uvicorn.*",
    "alembic.*",
    "qdrant_client.*",
    "sentence_transformers.*",
    "transformers.*",
    "torch.*",
    "vllm.*",
    "celery.*",
]
ignore_missing_imports = true

# Coverage配置
[tool.coverage.run]
source = ["shared", "services"]
omit = [
    "*/migrations/*",
    "*/tests/*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
] 
