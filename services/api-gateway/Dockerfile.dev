FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
COPY ../../requirements.txt /tmp/requirements.txt

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r /tmp/requirements.txt

# 复制源代码
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app:/app/shared
ENV PYTHONUNBUFFERED=1
ENV ENV=development

# 暴露端口
EXPOSE 8000

# 开发模式启动命令（支持热重载）
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"] 