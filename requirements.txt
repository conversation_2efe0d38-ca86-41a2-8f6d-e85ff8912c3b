# Web框架
fastapi==0.115.6
uvicorn[standard]==0.32.1
websockets==14.1
starlette==0.41.3

# AI/ML框架
langchain==0.3.12
langchain-community==0.3.12
langchain-openai==0.2.14
openai==1.61.0
transformers==4.51.1
sentence-transformers==3.3.1
vllm==0.8.5
torch==2.6.0
torchvision>=0.16.0
torchaudio>=2.1.0

# 数据验证和ORM
pydantic==2.10.4
pydantic-settings==2.7.0
sqlalchemy==2.0.36
alembic==1.14.0
asyncpg==0.30.0
aiomysql==0.2.0

# 缓存和消息队列
redis==5.2.1
celery==5.4.0
kombu==5.4.2

# 向量数据库
qdrant-client==1.14.1

# 搜索引擎
elasticsearch==8.16.0

# HTTP客户端
httpx==0.28.1
aiohttp==3.11.11
requests==2.32.3

# 数据处理
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0
beautifulsoup4==4.12.3
lxml==5.3.0
pypdf==5.1.0
python-multipart==0.0.20

# 配置管理
python-dotenv==1.0.1
pyyaml==6.0.2
toml==0.10.2

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
cryptography==44.0.0

# 监控和日志
prometheus-client==0.21.1
structlog==24.4.0
loguru==0.7.3

# 时间处理
python-dateutil==2.9.0
pytz==2024.2

# 图像处理
pillow==11.0.0

# 模板引擎
jinja2==3.1.4

# 开发工具
rich==14.0.0
typer==0.15.1
click==8.1.8

# 测试工具
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-asyncio>=0.21.0
factory-boy==3.3.1
freezegun==1.5.3
responses==0.25.3

# 代码质量
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
pre-commit>=3.0.0

# 类型提示
mypy==1.14.0
types-redis==4.6.0.20241004
types-requests==2.32.4.20250611

# 数据库工具
mysql-connector-python>=8.0.33
